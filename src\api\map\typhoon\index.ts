// src/api/map/typhoon/index.ts
import request from '@/config/axios'

// 台风基本信息接口
export interface TyphoonInfoVO {
  id: number
  englishName: string
  chineseName: string
  typhoonNumber: string
  typhoonCode: string
  referenceId: string | null
  meaning: string
  status: string
  year: number
}

// 台风轨迹点接口（根据实际轨迹数据结构调整）
export interface TyphoonTrackVO {
  id: number
  typhoonId: number
  timeStr: string     // 时间字符串，例如 "202411111800"
  longitude: number   // 经度
  latitude: number    // 纬度
  level: string       // 台风等级，例如 "TS"
  windSpeed: number   // 风速
  pressure: number    // 气压
  direction: string   // 移动方向，例如 "WNW"
  speed: number       // 移动速度
  trackId: number     // 轨迹ID
  timestampMs: number // 时间戳（毫秒）
}

// 根据年份查询台风基本信息列表
export function getTyphoonListByYear(year: number) {
  return request.get({
    url: '/3dmap/typhoon/info/list-by-year',
    params: { year }
  })
}

// 根据台风ID查询台风轨迹坐标列表
export function getTyphoonTrackByTyphoonId(typhoonId: number) {
  return request.get({
    url: '/3dmap/typhoon/track/list-by-typhoon-id',
    params: { typhoonId }
  })
}