<!-- Windpowermodel.vue -->
<template>
  <div class="wind-power-control">
    <button @click="toggleMenu" class="cesium-button cesium-button-wide">
      风电模型控制 ▼
    </button>
    
    <div v-show="showMenu" class="control-panel wind-model-panel">
      <div class="panel-header">
        <h3>风电模型控制</h3>
        <button @click="toggleMenu" class="close-button">×</button>
      </div>
      
      <div class="status-info" v-if="modelsAdded">
        <p>状态: 模型已添加</p>
        <p>模型数量: {{ windTurbineEntities.length }}</p>
        <p>当前模型: {{ currentModelType === 0 ? 'fengdian220' : 'fengdian240' }}</p>
      </div>
      
      <div class="button-container">
        <button @click="addWindTurbineModels" class="cesium-button">添加模型</button>
        <button @click="toggleAllModels" class="cesium-button">切换模型</button>
        <button @click="flyToWindTurbineModels" class="cesium-button">风电模型视角</button>
        <button @click="removeWindTurbineModels" class="cesium-button">清除模型</button>
      </div>
    </div>
  </div>
</template>

<script>
import * as Cesium from 'cesium';

export default {
  name: "WindPowerModel",
  props: {
    viewer: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      modelsAdded: false,
      showMenu: false,
      windTurbineEntities: [],
      centerLongitude: 113.443948532999,
      centerLatitude: 21.9015959202157,
      modelCount: 20,
      spacing: 1000, // 1000米间距
      currentModelType: 0 // 0 表示 fengdian220，1 表示 fengdian240
    }
  },
  beforeUnmount() {
    // 组件销毁前清除模型
    this.removeWindTurbineModels();
  },
  methods: {
    toggleMenu() {
      this.showMenu = !this.showMenu;
    },
    
    createModel(url, longitude, latitude, height) {
      // 检查模型文件是否存在（尝试加载）
      console.log('正在尝试加载模型:', url);
      
      const position = Cesium.Cartesian3.fromDegrees(
        longitude,
        latitude,
        height,
      ); 
      const heading = Cesium.Math.toRadians(0);
      const pitch = 0;
      const roll = 0;
      const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
      const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr);

      const entity = this.viewer.entities.add({
        name: url,
        position: position,
        orientation: orientation,
        model: {
          uri: url,
          scale: 1, // 固定缩放比例
          minimumPixelSize: 0, // 设置为0，禁用最小像素大小
          maximumScale: undefined, // 移除最大缩放限制
          allowPicking: true
        },
      });
      
      // 正确监听模型加载状态
      if (entity.model && typeof entity.model.then === 'function') {
        entity.model.then(function(model) {
          console.log('模型加载成功:', url);
        }).catch(function(error) {
          console.error('模型加载失败:', url, error);
        });
      } else if (entity.model && entity.model.readyPromise) {
        entity.model.readyPromise.then(function(model) {
          console.log('模型加载成功:', url);
        }).catch(function(error) {
          console.error('模型加载失败:', url, error);
        });
      } else {
        // 如果无法监听加载状态，则设置一个定时器检查
        setTimeout(() => {
          if (entity.model && entity.model.ready) {
            console.log('模型加载成功:', url);
          } else {
            console.warn('无法确定模型加载状态:', url);
          }
        }, 1000);
      }
      
      return entity;
    },
    
    addWindTurbineModels() {
      if (this.modelsAdded) {
        console.log('模型已经添加过了');
        return;
      }
      
      console.log('开始添加风力发电模型...');
      try {
        const entities = [];
        
        // 计算网格布局参数
        const gridSize = Math.ceil(Math.sqrt(this.modelCount)); // 网格大小
        const startX = this.centerLongitude - (gridSize * this.spacing / 2) / 100000; // 起始经度
        const startY = this.centerLatitude - (gridSize * this.spacing / 2) / 100000; // 起始纬度
        
        // 使用网格布局放置模型
        for (let i = 0; i < this.modelCount; i++) {
          // 计算行列位置
          const row = Math.floor(i / gridSize);
          const col = i % gridSize;
          
          // 计算经纬度位置
          // 注意：这里需要根据地球曲率进行近似计算
          const longitude = startX + (col * this.spacing / 100000); // 简化计算
          const latitude = startY + (row * this.spacing / 100000); // 简化计算
          
          // 使用当前模型类型
          const modelUrl = this.currentModelType === 0 ? 
            "/models/fengdian220.glb" : 
            "/models/fengdian240.glb";
          
          const entity = this.createModel(
            modelUrl, 
            longitude, 
            latitude, 
            0
          );
          
          entities.push(entity);
        }
        
        this.windTurbineEntities = entities;
        this.modelsAdded = true;
        console.log(`风力发电模型添加完成，共添加 ${entities.length} 个模型`);
      } catch (error) {
        console.error('添加模型时出错:', error);
      }
    },
    
    // 切换所有模型为另一个模型（优化版本）
    toggleAllModels() {
      if (!this.modelsAdded) {
        console.log('请先添加模型');
        return;
      }
      
      console.log('切换所有模型...');
      
      // 切换模型类型
      this.currentModelType = this.currentModelType === 0 ? 1 : 0;
      
      // 直接更新现有模型的URI，而不是删除重建
      const modelUrl = this.currentModelType === 0 ? 
        "/models/fengdian220.glb" : 
        "/models/fengdian240.glb";
      
      this.windTurbineEntities.forEach(entity => {
        // 直接更新模型URI
        entity.model.uri = modelUrl;
      });
      
      console.log(`所有模型已切换为 ${modelUrl}`);
    },
    
    // 飞行到风电模型视角
    flyToWindTurbineModels() {
      if (this.modelsAdded && this.viewer) {
        console.log('飞向风力发电模型视角...');
        this.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(
            this.centerLongitude, 
            this.centerLatitude, 
            3000.0 // 调整高度以获得更好的视角
          ),
          orientation: {
            heading: Cesium.Math.toRadians(0.0),
            pitch: Cesium.Math.toRadians(-15.0),
            roll: 0.0
          },
          duration: 3 // 飞行时间3秒
        });
      } else {
        console.log('模型未添加，无法飞向视角');
      }
    },
    
    removeWindTurbineModels() {
      if (this.modelsAdded) {
        console.log('正在清除风力发电模型...');
        this.windTurbineEntities.forEach(entity => {
          this.viewer.entities.remove(entity);
        });
        this.windTurbineEntities = [];
        this.modelsAdded = false;
        console.log('风力发电模型已清除');
      }
    }
  }
}
</script>

<style scoped>
.cesium-button-wide {
  width: 100%;
  margin-bottom: 10px;
}

.wind-power-control {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.wind-model-panel {
  position: absolute;
  top: 60px;
  left: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 10px;
  color: white;
  min-width: 200px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ff4d4f;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.cesium-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100%;
}

.cesium-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.status-info {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  font-size: 12px;
}

.status-info p {
  margin: 5px 0;
}
</style>