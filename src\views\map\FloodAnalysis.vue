<!-- src/views/map/FloodAnalysis.vue -->
<template>
  <div>
    <!-- 淹没分析控制面板 -->
    <div class="flood-control-panel" v-show="showFloodPanel">
      <div class="panel-header">
        <h3>淹没分析</h3>
        <button @click="toggleFloodPanel" class="close-button">×</button>
      </div>
      
      <!-- 绘制按钮 -->
      <!-- <div class="control-group">
        <button 
          @click="startDrawing" 
          :disabled="isDrawing" 
          class="cesium-button draw-button"
          :class="{ active: isDrawing }"
        >
          {{ isDrawing ? '绘制中... 点击地图两点确定区域' : '绘制矩形区域' }}
        </button>
      </div> -->
      
      <!-- 添加默认区域按钮 -->
      <div class="control-group">
        <button @click="setDefaultFloodArea" class="cesium-button">
          开始分析
        </button>
      </div>
      
      <div class="control-group" v-if="floodArea">
        <!-- 修复：确保 floodHeight 是数字类型 -->
        <label>当前高度: {{ Number(floodHeight).toFixed(2) }} 米</label>
        <input 
          type="range" 
          v-model="floodHeight" 
          :min="minHeight" 
          :max="maxHeight" 
          step="0.1"
          @input="updateFloodHeight"
        />
      </div>
      
      <div class="control-group" v-if="floodArea">
        <label>淹没速度:</label>
        <input 
          type="range" 
          v-model="floodSpeed" 
          min="0.1" 
          max="5" 
          step="0.1"
          @change="updateFloodSpeed"
        />
        <span>{{ floodSpeed }}</span>
      </div>
      
      <div class="control-group" v-if="floodArea">
        <label>水体颜色:</label>
        <input 
          type="color" 
          v-model="floodColor" 
          @change="updateFloodColor"
        />
      </div>
      
      <div class="button-container" v-if="floodArea">
        <button @click="startFloodAnimation" class="cesium-button">
          {{ isFloodAnimating ? '暂停' : '开始' }} 淹没
        </button>
        <button @click="resetFlood" class="cesium-button">重置</button>
        <button @click="clearFlood" class="cesium-button">清除</button>
      </div>
    </div>
    
    <!-- 主控制按钮 -->
    <div class="main-control-panel flood-main-control">
      <div class="button-group">
        <button @click="toggleFloodPanel" class="cesium-button main-button">
          淹没分析 ▼
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import * as Cesium from 'cesium';

export default {
  name: "FloodAnalysis",
  props: {
    viewer: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showFloodPanel: false,
      isDrawing: false,
      floodEntities: [],
      floodHeight: 0, // 确保初始值是数字
      minHeight: 0,
      maxHeight: 100,
      floodSpeed: 1.0,
      floodColor: '#003366', // 改为标准十六进制格式
      showNonFloodedArea: true,
      isFloodAnimating: false,
      animationId: null,
      floodArea: null,
      drawingHandler: null,
      floodEntity: null
    }
  },
  beforeUnmount() {
    this.clearFlood();
  },
  methods: {
    toggleFloodPanel() {
      this.showFloodPanel = !this.showFloodPanel;
       // 当打开面板时，移动视角到指定位置
      if (this.showFloodPanel) {
        this.moveToFloodArea();
      }
    },
    
    // 新增设置默认区域的方法
    setDefaultFloodArea() {
      // 清除之前的淹没效果
      this.clearFlood();
      
      // 定义默认区域范围
      const west = Cesium.Math.toRadians(112.19182);
      const east = Cesium.Math.toRadians(112.20782);
      const south = Cesium.Math.toRadians(21.77380);
      const north = Cesium.Math.toRadians(21.79432);
      
      this.floodArea = { west, east, south, north };
      
      // 设置默认高度范围
      this.minHeight = 0;
      this.maxHeight = 100;
      // 确保 floodHeight 是数字类型
      this.floodHeight = 0;
      
      // 创建淹没效果
      this.createFloodEntity();
    
    
    },

    // 新增方法：移动视角到淹没分析区域
    moveToFloodArea() {
      if (!this.viewer) return;
      
      // 目标位置参数
      const destination = Cesium.Cartesian3.fromDegrees(112.19162, 21.79021, 168.25);
      
      // 设置相机视角
      this.viewer.camera.flyTo({
        destination: destination,
        orientation: {
          heading: Cesium.Math.toRadians(128.86), // 偏航角
          pitch: Cesium.Math.toRadians(-7.92),   // 俯仰角
          roll: 0.0                              // 翻滚角
        },
        duration: 3 // 飞行时间（秒）
      });
    },
    
    startDrawing() {
      if (!this.viewer) return;
      
      this.isDrawing = true;
      
      // 清除之前的淹没效果
      this.clearFlood();
      
      // 使用Cesium的绘图工具
      this.drawingHandler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
      
      let clickCount = 0;
      const positions = [];
      
      // 添加点击事件处理
      this.drawingHandler.setInputAction((click) => {
        if (clickCount >= 2) return;
        
        const cartesian = this.viewer.camera.pickEllipsoid(click.position, this.viewer.scene.globe.ellipsoid);
        if (cartesian) {
          const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
          positions.push(cartographic);
          clickCount++;
          
          if (clickCount === 1) {
            // 第一次点击，创建临时矩形
            this.createTemporaryRectangle(positions[0]);
          } else if (clickCount === 2) {
            // 第二次点击，完成绘制
            this.finishDrawing(positions[0], positions[1]);
            if (this.drawingHandler) {
              this.drawingHandler.destroy();
              this.drawingHandler = null;
            }
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    },
    
    createTemporaryRectangle(firstPoint) {
      // 创建临时矩形以提供视觉反馈
      const tempEntity = this.viewer.entities.add({
        name: 'flood_analysis_temp',
        rectangle: {
          coordinates: new Cesium.CallbackProperty(() => {
            // 这里只是一个占位，实际会在鼠标移动时更新
            return null;
          }, false),
          material: Cesium.Color.fromCssColorString(this.floodColor).withAlpha(0.3),
          height: 0
        }
      });
      
      this.floodEntities.push(tempEntity);
    },
    
    async finishDrawing(firstPoint, secondPoint) {
      if (!firstPoint || !secondPoint) return;
      
      this.isDrawing = false;
      
      // 计算矩形范围
      const west = Math.min(firstPoint.longitude, secondPoint.longitude);
      const east = Math.max(firstPoint.longitude, secondPoint.longitude);
      const south = Math.min(firstPoint.latitude, secondPoint.latitude);
      const north = Math.max(firstPoint.latitude, secondPoint.latitude);
      
      this.floodArea = { west, east, south, north };
      
      // 获取高程范围 (简化处理)
      try {
        // 在实际应用中，应该采样区域内的高程数据
        const minHeight = Math.min(firstPoint.height || 0, secondPoint.height || 0);
        const maxHeight = Math.max(firstPoint.height || 0, secondPoint.height || 0) + 100;
        
        this.minHeight = minHeight;
        this.maxHeight = maxHeight;
        // 确保 floodHeight 是数字类型
        this.floodHeight = Number(minHeight);
        
        // 创建淹没效果
        this.createFloodEntity();
      } catch (error) {
        console.error('Error calculating elevation range:', error);
      }
      
      // 清理临时实体
      this.clearDrawingEntities();
    },
    
    createFloodEntity() {
      if (!this.viewer || !this.floodArea) return;
      
      // 移除现有的淹没实体
      if (this.floodEntity) {
        this.viewer.entities.remove(this.floodEntity);
      }
      
      const { west, east, south, north } = this.floodArea;
      
      // 使用回调属性确保高度可以动态更新
      this.floodEntity = this.viewer.entities.add({
        name: 'flood_entity',
        rectangle: {
          coordinates: new Cesium.Rectangle(west, south, east, north),
          height: new Cesium.CallbackProperty(() => Number(this.floodHeight), false),
          material: new Cesium.Color.fromCssColorString(this.floodColor).withAlpha(0.6)
        }
      });
    },
    
    updateFloodHeight() {
      // 由于使用了CallbackProperty，不需要手动更新实体
      // Cesium会自动在每一帧调用CallbackProperty函数获取当前值
    },
    
    updateFloodSpeed() {
      // 速度更新在动画中使用
    },
    
    updateFloodColor() {
      if (this.floodEntity && this.floodEntity.rectangle) {
        this.floodEntity.rectangle.material = new Cesium.Color.fromCssColorString(this.floodColor).withAlpha(0.6);
      }
    },
    
    startFloodAnimation() {
      if (!this.viewer) return;
      
      if (this.isFloodAnimating) {
        // 暂停动画
        this.isFloodAnimating = false;
        if (this.animationId) {
          cancelAnimationFrame(this.animationId);
          this.animationId = null;
        }
      } else {
        // 开始动画
        this.isFloodAnimating = true;
        this.animateFlood();
      }
    },
    
    animateFlood() {
      if (!this.isFloodAnimating) return;
      
      // 确保 floodHeight 是数字类型
      this.floodHeight = Number(this.floodHeight) + Number(this.floodSpeed) * 0.1;
      
      if (this.floodHeight > this.maxHeight) {
        this.floodHeight = this.minHeight;
      }
      
      // 不需要手动调用updateFloodHeight，CallbackProperty会自动处理
      
      // 继续下一帧动画
      if (this.isFloodAnimating) {
        this.animationId = requestAnimationFrame(() => {
          this.animateFlood();
        });
      }
    },
    
    resetFlood() {
      // 确保 floodHeight 是数字类型
      this.floodHeight = Number(this.minHeight);
      // 不需要手动调用updateFloodHeight，CallbackProperty会自动处理
      
      if (this.isFloodAnimating) {
        this.isFloodAnimating = false;
        if (this.animationId) {
          cancelAnimationFrame(this.animationId);
          this.animationId = null;
        }
      }
    },
    
    clearFlood() {
      // 清除淹没效果
      if (this.floodEntity) {
        this.viewer.entities.remove(this.floodEntity);
        this.floodEntity = null;
      }
      
      this.clearDrawingEntities();
      
      // 销毁绘图处理器
      if (this.drawingHandler) {
        this.drawingHandler.destroy();
        this.drawingHandler = null;
      }
      
      // 停止动画
      if (this.isFloodAnimating) {
        this.isFloodAnimating = false;
      }
      
      if (this.animationId) {
        cancelAnimationFrame(this.animationId);
        this.animationId = null;
      }
      
      this.isDrawing = false;
      this.floodArea = null;
    },
    
    clearDrawingEntities() {
      // 清除绘制的实体
      this.floodEntities.forEach(entity => {
        this.viewer.entities.remove(entity);
      });
      this.floodEntities = [];
    }
  },
}
</script>

<style scoped>
.flood-control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 10px;
  color: white;
  min-width: 300px;
  font-size: 14px;
  font-family: 宋体;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.flood-main-control {
  top: 70px;
  right: 30px;
  left: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ff4d4f;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.control-group input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
}

.control-group input[type="color"] {
  width: 100%;
  height: 30px;
  border: none;
  border-radius: 4px;
}

.control-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.control-group span {
  display: block;
  text-align: right;
  font-size: 12px;
  color: #aaa;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.draw-button {
  width: 100%;
  padding: 10px;
}

.cesium-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100%;
}

.cesium-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.cesium-button:disabled {
  background-color: rgba(128, 128, 128, 0.8);
  cursor: not-allowed;
}

.cesium-button.active {
  background-color: rgba(255, 0, 0, 0.8);
}

.main-control-panel {
  position: absolute;
  z-index: 1000;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 120px;
}

.main-button:hover {
  background-color: rgba(64, 158, 255, 1);
}
</style>