<template>
  <div id="cesiumContainer" class="fullSize"></div>
  
  <!-- 主控制面板 -->
  <div class="main-control-panel">
    <div class="button-group">

      <!-- 电子围栏组件 -->
      <Fence 
        :viewer="viewer" 
        :visible="showFence" 
        :fence-data="windBufferFenceData"
        :show-fence-menu="showFenceMenu"
        :show-fence="showFence"
        :show-wind-buffer="showWindBuffer"
        @update:wind-buffer-data-source="handleWindBufferDataSourceUpdate"
        @update:wind-buffer-fence-data="handleWindBufferFenceDataUpdate"
        @toggle-fence-menu="toggleFenceMenu"
        @toggle-fence="toggleFence"
        @toggle-wind-buffer="toggleWindBuffer"
        ref="fenceComponent" 
      />
    </div>
  </div>
  
  <FloodAnalysis 
    :viewer="viewer" 
    ref="floodAnalysis"
  />

  <!-- 新增台风组件 -->
  <Typhoon 
    v-if="viewer" 
    :viewer="viewer" 
    ref="typhoon"
  />

  <!-- 风力发电模型组件 -->
      <WindPowerModel 
        :viewer="viewer" 
        ref="windPowerModel"
      />

  <div class="navigation-buttons">
    <button @click="flyToModelPosition" class="cesium-button nav-button">模型位置</button>
    <button @click="flyToCoastline" class="cesium-button nav-button">海岸线</button>
  </div>
</template>

<script>
import * as Cesium from 'cesium'
import FloodAnalysis from './FloodAnalysis.vue'
import Typhoon from './Typhoon.vue';
import Fence from './Fence.vue'; 
import WindPowerModel from './Windpowermodel.vue'; // 添加导入

export default {
  name: "Mapmanagement",
  components: {
    FloodAnalysis,
    Typhoon,
     WindPowerModel,
    Fence
  },
  data() {
    return {
      viewer: null,
      showFence: false,
      showFenceMenu: false, 
      showWindBuffer: false,
      windBufferDataSource: null,
      windBufferFenceData: [], // 存储风电缓冲区围栏数据
      waterPrimitive: null,
      showWaterMenu: false,
      waterParams: {
        frequency: 15000.0,
        animationSpeed: 0.05,
        amplitude: 10.0,
        specularIntensity: 1.0,
        velocityX: 0.0,
        velocityY: 0.0
      },
      // 添加默认水面参数
      defaultWaterParams: {
        frequency: 15000.0,
        animationSpeed: 0.05,
        amplitude: 10.0,
        specularIntensity: 1.0,
        velocityX: 0.0,
        velocityY: 0.0
      }
    }
  },
  watch: {
    waterParams: {
      handler(newParams) {
        if (this.waterPrimitive && this.waterPrimitive.appearance.material) {
           const material = this.waterPrimitive.appearance.material;
           material.uniforms.frequency = newParams.frequency;
           material.uniforms.animationSpeed = newParams.animationSpeed;
           material.uniforms.amplitude = newParams.amplitude;
        }
      },
      deep: true
    }
  },
  mounted() {
    // 初始化 Cesium Viewer 和地形加载逻辑
    this.initCesium();
  },
  methods: {
    // 添加切换电子围栏菜单的方法
    toggleFenceMenu() {
      this.showFenceMenu = !this.showFenceMenu;
    },
    
    // 切换风电缓冲区显示
    toggleWindBuffer() {
      this.showWindBuffer = !this.showWindBuffer;
      // 调用 Fence 组件的方法
      if (this.$refs.fenceComponent) {
        this.$refs.fenceComponent.toggleWindBuffer(this.showWindBuffer);
      }
    },

    // 处理风电缓冲区数据源更新
    handleWindBufferDataSourceUpdate(dataSource) {
      this.windBufferDataSource = dataSource;
    },

    // 处理风电缓冲区围栏数据更新
    handleWindBufferFenceDataUpdate(fenceData) {
      this.windBufferFenceData = fenceData;
    },

    // 添加切换电子围栏的方法
    toggleFence() {
      this.showFence = !this.showFence;
    },
    
    // 添加恢复默认水面参数的方法
    resetWaterParams() {
      this.waterParams = { ...this.defaultWaterParams };
    },

    async initCesium() {
      // Cesium ion的Token，虽然加载这个地形不需要，但最好还是设置一个
      Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.1DfKMIH0lfRC4vPR8acNlUBQMtjWvuN_MInN7hUETsw'
      
      //地形数据加载 → 地形数据加载完成 → 创建 Viewer 并传入地形数据 → Cesium 使用该地形数据渲染场景
      // 1. 初始化Viewer，关键：terrainProvider设置为undefined
      // 1. 创建Viewer，并禁用默认地形
      Cesium.CesiumTerrainProvider.fromUrl(
        'demodata/cesiumTerrain/%E5%8D%97%E6%B5%B7', {
        requestVertexNormals: true,
      }).then(terrainProvider => {
        // .then() 内部的代码会在 Promise 成功解析后执行
        // 此时 terrainProvider 是已经加载好的地形提供者
        this.viewer = new Cesium.Viewer('cesiumContainer', {
          terrainProvider: terrainProvider,//添加自定义地形
          animation:false,//动画小部件
          imageryProvider: false,//禁用影像
          infoBox: false, // 禁用 InfoBox
          selectionIndicator: false, // 禁用选择指示器
          shadows: false, // 禁用阴影
          shouldAnimate: true, // 启用动画（水面效果需要）
          sceneModePicker: false, // 禁用场景模式选择器
          timeline: false, // 禁用时间线
          navigationHelpButton: false, // 禁用导航帮助按钮
          homeButton: false, // 禁用 Home 按钮
          fullscreenButton: false, // 禁用全屏按钮
          geocoder: false, // 禁用地理编码器
          baseLayerPicker: false // 禁用基础图层选择器
        });
        
        // 隐藏版权信息
        this.viewer.scene.frameState.creditDisplay.container.style.display = "none";
        
        this.viewer.scene.globe.depthTestAgainstTerrain = false;//禁用地形深度测试
        this.viewer.scene.skyAtmosphere.show = true;//启用天体
        
        // 移除默认影像图层
        this.viewer.imageryLayers.removeAll();
        
        // 添加天地图影像服务
        const tiandituKey = 'c71477a3f164d6938566310e4c5df556';
        
        // 集成 44.html 中的 GeoServer 影像服务
        const provider = new Cesium.WebMapTileServiceImageryProvider({
            url: "/geoserver/gwc/service/wmts/rest/Image2023/{style}/{TileMatrixSet}/EPSG:4490_Image2023:{TileMatrix}/{TileRow}/{TileCol}?format=image/jpeg",
            layer: 'Image2003',
            style: 'default',
            format: 'image/jpeg',
            tileMatrixSetID: 'EPSG:4490_Image2023',
            tileMatrixLabels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'],

            tilingScheme: new Cesium.GeographicTilingScheme(),
            maximumLevel: 20
        });
        this.viewer.imageryLayers.addImageryProvider(provider);
        
        // 集成 44.html 中的天地图注记图层（带裁剪）
        const tiandituLabelProvider = new Cesium.WebMapTileServiceImageryProvider({
            url: "http://t0.tianditu.gov.cn/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=" + tiandituKey,
            layer: "tdtCiaLayer",
            style: "default",
            format: "image/png",
            tileMatrixSetID: "GoogleMapsCompatible",
            maximumLevel: 18,
            show: true
        });
        const chinaRectangle = Cesium.Rectangle.fromDegrees(
            109.75,  // West: 最西经度
            20.15,  // South: 最南纬度
            117.3333, // East: 最东经度
            25.5167  // North: 最北纬度
        );
        const tiandituLabelLayer = new Cesium.ImageryLayer(tiandituLabelProvider, {
            // 核心代码在这里！
            rectangle: chinaRectangle,

            // 你还可以设置其他显示选项，例如透明度
            alpha: 0.9,
            show: true
        });

        // --- 步骤 4: 将裁剪后的图层添加到 Viewer ---
        this.viewer.imageryLayers.add(tiandituLabelLayer);
        
        // 定义高程颜色材质
        const elevationColorMaterial = new Cesium.Material({
          fabric: {
            type: 'ElevationColor',
            uniforms: {
              alpha: 0.3
            },
            source: `
              czm_material czm_getMaterial(czm_materialInput materialInput)
              {
                czm_material material = czm_getDefaultMaterial(materialInput);
                float height = materialInput.height;
                vec3 finalColor;

                if (height > 5500.0) {
                  finalColor = vec3(1.0, 1.0, 1.0);
                } else if (height > 4000.0) {
                  finalColor = mix(vec3(0.0, 0.784, 0.784), vec3(1.0, 1.0, 1.0), (height - 4000.0) / (5500.0 - 4000.0));
                } else if (height > 2000.0) {
                  finalColor = mix(vec3(0.667, 0.780, 0.667), vec3(0.0, 0.784, 0.784), (height - 2000.0) / (4000.0 - 2000.0));
                } else if (height > 1300.0) {
                  finalColor = mix(vec3(0.784, 0.863, 0.784), vec3(0.667, 0.780, 0.667), (height - 1300.0) / (2000.0 - 1300.0));
                } else if (height > 500.0) {
                  finalColor = mix(vec3(0.776, 0.890, 0.776), vec3(0.784, 0.863, 0.784), (height - 500.0) / (1300.0 - 500.0));
                } else if (height > 0.0) {
                  finalColor = mix(vec3(0.992, 0.992, 0.992), vec3(0.776, 0.890, 0.776), (height - 0.0) / (500.0 - 0.0));
                } else if (height > -10.0) {
                  finalColor = mix(vec3(0.678, 0.890, 0.953), vec3(0.992, 0.992, 0.992), (height - (-10.0)) / (0.0 - (-10.0)));
                } else if (height > -1000.0) {
                  finalColor = mix(vec3(0.349, 0.737, 0.882), vec3(0.678, 0.890, 0.953), (height - (-1000.0)) / (-10.0 - (-1000.0)));
                } else if (height > -3000.0) {
                  finalColor = mix(vec3(0.188, 0.580, 0.788), vec3(0.349, 0.737, 0.882), (height - (-3000.0)) / (-1000.0 - (-3000.0)));
                } else if (height > -5000.0) {
                  finalColor = mix(vec3(0.169, 0.416, 0.690), vec3(0.188, 0.580, 0.788), (height - (-5000.0)) / (-3000.0 - (-5000.0)));
                } else if (height > -7000.0) {
                  finalColor = mix(vec3(0.133, 0.282, 0.635), vec3(0.169, 0.416, 0.690), (height - (-7000.0)) / (-5000.0 - (-7000.0)));
                } else {
                  finalColor = mix(vec3(0.125, 0.333, 0.675), vec3(0.133, 0.282, 0.635), (height - (-10000.0)) / (-7000.0 - (-10000.0)));
                }
                
                material.diffuse = finalColor;
                material.alpha = alpha;
                
                return material;
              }
            `
          }
        });

        // 将自定义材质应用到地球上
        this.viewer.scene.globe.material = elevationColorMaterial;
        
        console.log("Viewer has been initialized with custom terrain.");

        // 添加GeoJSON数据（带水面效果）
        this.loadGeoJsonDataWithWaterEffect(this.viewer);
        
        // 加载 3D Tileset 模型
        this.load3DTilesetModel(this.viewer);
      }).catch(error => {
        console.error("Failed to load terrain:", error);

        console.log("Initializing Viewer with default ellipsoid terrain.");
        this.viewer = new Cesium.Viewer('cesiumContainer', {
          shouldAnimate: true // 启用动画（水面效果需要）
        });
        
        // 添加GeoJSON数据（带水面效果）
        this.loadGeoJsonDataWithWaterEffect(this.viewer);
        
        // 加载 3D Tileset 模型
        this.load3DTilesetModel(this.viewer);
      });
    },

    // 加载GeoJSON数据并应用水面效果
    async loadGeoJsonDataWithWaterEffect(viewer) {
      try {
        // 加载GeoJSON数据，但不自动添加到viewer
        const geoJsonDataSource = new Cesium.GeoJsonDataSource();
        await geoJsonDataSource.load('/gdsealevel/gdsealevel.json');
        
        // 获取实体并设置水面效果
        const entities = geoJsonDataSource.entities.values;
        let instances = [];

        for (let i = 0; i < entities.length; i++) {
          const entity = entities[i];
          if (entity.polygon) {
            let geometry = new Cesium.GeometryInstance({
              geometry: new Cesium.PolygonGeometry({
                polygonHierarchy: new Cesium.PolygonHierarchy(
                  entity.polygon.hierarchy.getValue().positions
                ),
                extrudedHeight: 0,
                // 设置一个固定高度，取消贴地效果
                height: 0, // 调整为贴地
                vertexFormat: Cesium.VertexFormat.POSITION_AND_ST, // 使用POSITION_AND_ST格式以支持材质
              }),
            });
            instances.push(geometry);
          }
        }

        // 使用带有水面材质的MaterialAppearance
        const waterMaterial = new Cesium.Material({
          fabric: {
            type: "Water",
            uniforms: {
              normalMap: "/waterNormals.jpg",
              frequency: this.waterParams.frequency,
              animationSpeed: this.waterParams.animationSpeed,
              amplitude: this.waterParams.amplitude,
              baseWaterColor: new Cesium.Color(0.2, 0.3, 0.7, 0.8),
              blendColor: new Cesium.Color(0.1, 0.3, 0.5, 0.5)
            },
          },
        });
        // 创建带有水面材质的Primitive
        this.waterPrimitive = new Cesium.Primitive({
          geometryInstances: instances,
          appearance: new Cesium.MaterialAppearance({
            material: waterMaterial,
            faceForward: true,
            translucent: true
          }),
        });

        this.viewer.scene.primitives.add(this.waterPrimitive);
        
        console.log("GeoJSON data with water effect loaded successfully.");
      } catch (error) {
        console.error("Failed to load GeoJSON data:", error);
      }
    },
    
    // 加载 3D Tileset 模型
    async load3DTilesetModel(viewer) {
      try {
        const tileset = await Cesium.Cesium3DTileset.fromUrl(
          "http://192.168.110.41:9109/ts/tiles/OSGB/tileset.json"
        );

        tileset.modelMatrix = Cesium.Matrix4.fromArray([
          1, -5.551115123125783e-17, 2.7755575615628914e-17, 0,
          -5.551115123125783e-17, 1, 0, 0,
          2.7755575615628914e-17, 0, 1, 0,
          // -110, 150, 130, 1
          -4.656612873077393e-10, 9.313225746154785e-10, 40, 1
        ]);

        viewer.scene.primitives.add(tileset);

        console.log("3D Tileset model loaded successfully.");
      } catch (error) {
        console.error("Failed to load 3D Tileset model:", error);
      }
    },

    // 飞向模型位置
    flyToModelPosition() {
      if (this.viewer) {
        this.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(112.19153,21.80415,882.77),
          orientation: {
            heading: Cesium.Math.toRadians(158.07),
            pitch: Cesium.Math.toRadians(-23.54),
            roll: 0.0
          },
          duration: 3
        });
      }
    },

    // 飞向海岸线位置
    flyToCoastline() {
      if (this.viewer) {
        this.viewer.camera.flyTo({
          destination: Cesium.Cartesian3.fromDegrees(113.08742,18.69837,178255.59),
          orientation: {
            heading: Cesium.Math.toRadians(349.06),
            pitch: Cesium.Math.toRadians(-26.22),
            roll: 0.0
          },
          duration: 3
        });
      }
    },

    // 切换水面设置菜单
    toggleWaterMenu() {
      this.showWaterMenu = !this.showWaterMenu;
    }
  }
};
</script>

<style scoped>
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.main-control-panel {
  position: absolute;
  top: 20px;
  right: 30px;
  z-index: 1000;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 120px;
}

.main-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.control-panel {
  position: absolute;
  top: 70px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 10px;
  color: white;
  min-width: 300px;
  font-size: 14px;
  font-family: 宋体;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.water-panel {
  top: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ff4d4f;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.control-group input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
}

.control-group span {
  display: block;
  text-align: right;
  font-size: 12px;
  color: #aaa;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.cesium-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 120px;
}

.cesium-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.navigation-buttons {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nav-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100px;
}

.nav-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

/* 电子围栏面板样式 */
.fence-panel {
  top: 70px;
  right: 30px;
  left: auto;
}

/* 风电模型面板样式 */
.wind-model-panel {
  top: 70px;
  right: 30px;
  left: auto;
}
</style>