<!-- src/views/map/typhoon.vue -->
<template>
  <div>
    <!-- 台风控制面板 -->
    <div class="typhoon-control-panel" v-show="showTyphoonPanel">
      <div class="panel-header">
        <h3>台风模拟</h3>
        <button @click="toggleTyphoonPanel" class="close-button">×</button>
      </div>
      
      <!-- 年份选择和台风列表 -->
      <div class="typhoon-selector">
        <div class="year-selector">
          <label>选择年份：</label>
          <select v-model="selectedYear" @change="loadTyphoonList">
            <option v-for="year in availableYears" :key="year" :value="year">
              {{ year }}
            </option>
          </select>
          <button @click="loadTyphoonList" class="cesium-button">加载</button>
        </div>
        
        <div class="typhoon-list" v-if="typhoonList.length > 0">
          <h4>{{ selectedYear }}年台风列表</h4>
          <ul class="typhoon-items">
            <li 
              v-for="typhoon in typhoonList" 
              :key="typhoon.id"
              @click="selectTyphoon(typhoon)"
              :class="{ active: selectedTyphoon && selectedTyphoon.id === typhoon.id }"
            >
              <div class="typhoon-basic-info">
                <span class="typhoon-name">{{ typhoon.chineseName }}</span>
                <span class="typhoon-en-name">{{ typhoon.englishName }}</span>
              </div>
              <span class="typhoon-code">{{ typhoon.typhoonCode }}</span>
            </li>
          </ul>
        </div>
        
        <div v-else-if="loadingTyphoons" class="loading">
          加载中...
        </div>
        
        <div v-else class="no-data">
          暂无台风数据
        </div>
      </div>
      
      <!-- 台风操作按钮组 -->
      <div class="button-group">
        <div class="button-row">
          <button @click="loadTyphoonTrack" class="cesium-button" :disabled="!selectedTyphoon">加载台风数据</button>
          <button @click="toggleTyphoonAnimation" class="cesium-button" :disabled="!typhoonTrackPoints.length">
            {{ isPlaying ? '暂停' : '播放' }}
          </button>
        </div>
        <div class="button-row">
          <button @click="clearTyphoon" class="cesium-button clear-button">清除台风</button>
        </div>
      </div>
      
      <!-- 台风详细信息列表 -->
      <div class="typhoon-detail" v-if="selectedTyphoon && typhoonTrackPoints.length > 0">
        <h4>{{ selectedTyphoon.chineseName }} 台风信息</h4>
        <div class="detail-table-container">
          <table class="detail-table">
            <thead>
              <tr>
                <th>时间</th>
                <th>风速(m/s)</th>
                <th>移向</th>
                <th>等级</th>
              </tr>
            </thead>
            <tbody>
              <tr 
                v-for="(point, index) in typhoonTrackPoints" 
                :key="index"
                @click="focusOnTyphoonPoint(point)"
                @mouseenter="showPointInfoBubble(point)"
                @mouseleave="hidePointInfoBubble"
                class="typhoon-track-row"
              >
                <td>{{ formatTimeString(point.timeStr) }}</td>
                <td>{{ point.windSpeed }}</td>
                <td>{{ getDirectionText(point.direction) }}</td>
                <td>{{ getTyphoonLevelText(point.level) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- 台风图例 -->
      <div class="legend" v-if="showLegend">
        <h4>台风强度图例</h4>
        <div class="legend-item">
          <span class="legend-color" style="background: YELLOW;"></span>
          <span>热带低压</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: BLUE;"></span>
          <span>热带风暴</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: GREEN;"></span>
          <span>强热带风暴</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: ORANGE;"></span>
          <span>台风</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: MAGENTA;"></span>
          <span>强台风</span>
        </div>
        <div class="legend-item">
          <span class="legend-color" style="background: RED;"></span>
          <span>超强台风</span>
        </div>
      </div>
    </div>
    
    <!-- 主控制按钮 -->
    <div class="main-control-panel typhoon-main-control">
      <div class="button-group">
        <button @click="toggleTyphoonPanel" class="cesium-button main-button">
          台风模拟 ▼
        </button>
      </div>
    </div>
    
    <!-- 信息气泡 -->
    <div 
      id="typhoon-info-bubble" 
      class="info-bubble" 
      :style="{ left: bubblePosition.x + 'px', top: bubblePosition.y + 'px' }"
      v-show="showInfoBubble"
    >
      <div class="bubble-content">
        <div class="bubble-header">
          <h4>台风信息</h4>
        </div>
        <div class="bubble-body" v-html="bubbleContent"></div>
      </div>
      <div class="bubble-arrow"></div>
    </div>
  </div>
</template>

<script>
import * as Cesium from 'cesium'
import SuperGif from '@/utils/cesiumCtrl/LoadGif.js'
import { getTyphoonListByYear, getTyphoonTrackByTyphoonId } from '@/api/map/typhoon' // 引入API函数

export default {
  name: 'Typhoon',
  props: {
    viewer: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showTyphoonPanel: false,
      fengquanLayers: [],
      myEntityCollection: null,
      iii: 0,
      currentPointObj: null,
      tbentity: null,
      typhoonInterval: null,
      showLegend: true,
      // 存储警戒线和路径线实体
      typhoonEntities: [],
      // 存储 GIF 相关对象
      typhoonGif: null,
      typhoonGifElement: null,
      
      // 台风列表相关数据
      availableYears: [], // 可选年份
      selectedYear: new Date().getFullYear(), // 默认选中当前年份
      typhoonList: [], // 台风列表
      selectedTyphoon: null, // 选中的台风
      loadingTyphoons: false, // 加载状态
      typhoonTrackPoints: [], // 台风轨迹点数据
      
      // 台风动画相关
      isPlaying: false, // 台风动画播放状态
      currentTrackIndex: 0, // 当前台风轨迹点索引
      typhoonAnimationInterval: null, // 台风动画定时器
      
      // 信息气泡相关
      infoEntity: null, // 用于显示信息气泡的实体
      keepHeight: 100000, // 保持的视角高度
      
      // 鼠标悬停处理相关
      handler: null, // Cesium 事件处理器
      
      // 新增的气泡相关数据
      showInfoBubble: false,
      bubbleContent: '',
      bubblePosition: { x: 0, y: 0 },
      
      // 实时跟踪点相关
      trackedPoint: null,
      trackedPointCartesian: null
    }
  },
  async mounted() {
    // 初始化可选年份（近10年）
    const currentYear = new Date().getFullYear();
    this.availableYears = Array.from({length: 10}, (_, i) => currentYear - i);
    
    // 加载默认年份的台风列表
    await this.loadTyphoonList();
    
    // 创建信息气泡实体
    this.infoEntity = this.viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(0, 0),
      show: false,
      billboard: {
        image: '/images/info-icon.png', // 可选的信息图标
        scale: 0.5,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -20)
      },
      label: {
        text: '',
        font: '14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        showBackground: true,
        backgroundColor: new Cesium.Color(0.1, 0.1, 0.1, 0.8),
        backgroundPadding: new Cesium.Cartesian2(5, 5)
      }
    });
    
    // 监听相机移动事件，获取当前高度
    this.viewer.camera.moveEnd.addEventListener(() => {
      // 获取当前相机高度并保存
      const currentPosition = this.viewer.camera.positionCartographic;
      this.keepHeight = currentPosition.height;
    });
    
    // 初始化鼠标事件处理器
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
    
    // 添加鼠标移动事件监听
    this.handler.setInputAction((movement) => {
      this.handleMouseMovement(movement.endPosition);
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    
    // 创建信息气泡容器
    this.createInfoBubble();
    
    // 添加帧监听器来更新气泡位置
    this.viewer.clock.onTick.addEventListener(this.updateBubblePosition);
  },
 
  methods: {
    toggleTyphoonPanel() {
      this.showTyphoonPanel = !this.showTyphoonPanel;
      // 当打开面板时，移动视角到指定位置并初始化警戒线
      if (this.showTyphoonPanel) {
        this.moveToTyphoonArea();
        // this.initJJ();
      }
    },
    
    // 新增方法：移动视角到台风分析区域
    moveToTyphoonArea() {
      if (!this.viewer) return;
      
      // 目标位置参数 (使用默认台风位置)
      const destination = Cesium.Cartesian3.fromDegrees(120, 25, 9000000);
      
      // 设置相机视角
      this.viewer.camera.flyTo({
        destination: destination,
        duration: 3 // 飞行时间（秒）
      });
    },
    
    // 警戒线
    initJJ() {
      // 清除之前的警戒线实体
      this.clearTyphoonEntities();
      
      // 24 线
      const line24 = this.viewer.entities.add({
        name: '24',
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray([
            127, 34,
            127, 22,
            119, 18,
            119, 11,
            113, 4.5,
            105, 0
          ]),
          width: 2,
          material: Cesium.Color.RED,
          clampToGround: true,
        }
      })
      this.typhoonEntities.push(line24);

      // 48 线
      const line48 = this.viewer.entities.add({
        name: '48',
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray([
            132, 34,
            132, 22,
            119, 0,
            105, 0
          ]),
          width: 2,
          material: Cesium.Color.YELLOW,
          clampToGround: true,
        }
      })
      this.typhoonEntities.push(line48);

      const label24 = this.viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(126.129019, 29.104287),
        label: {
          fillColor: Cesium.Color.RED,
          text: '24小时警戒线',
          font: '14pt monospace',
        }
      })
      this.typhoonEntities.push(label24);

      const label48 = this.viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(132, 20),
        label: {
          fillColor: Cesium.Color.YELLOW,
          text: '48小时警戒线',
          font: '14pt monospace',
        }
      })
      this.typhoonEntities.push(label48);
    },

    // 加载台风列表
    async loadTyphoonList() {
      try {
        this.loadingTyphoons = true;
        const response = await getTyphoonListByYear(this.selectedYear);
        this.typhoonList = response;
        this.selectedTyphoon = null; // 重置选中的台风
      } catch (error) {
        console.error("加载台风列表失败:", error);
        this.typhoonList = [];
      } finally {
        this.loadingTyphoons = false;
      }
    },

    // 选择台风
    selectTyphoon(typhoon) {
      this.selectedTyphoon = typhoon;
    },

    // 加载台风轨迹数据
    async loadTyphoonTrack() {
      if (!this.selectedTyphoon) {
        alert('请先选择一个台风');
        return;
      }

      try {
        console.log("开始加载台风轨迹数据，台风ID:", this.selectedTyphoon.id);
        
        // 获取台风轨迹数据
        const response = await getTyphoonTrackByTyphoonId(this.selectedTyphoon.id);
        console.log('API返回的原始数据:', response); // 调试信息
        
        // 显示台风轨迹前先清除之前的台风数据
        this.clearTyphoon();
        
        // 确保正确处理响应数据
        if (response && Array.isArray(response)) {
          console.log(`接收到 ${response.length} 个轨迹点`);
          
          // 确保数据正确赋值
          this.$set ? 
            this.$set(this, 'typhoonTrackPoints', [...response]) : 
            (this.typhoonTrackPoints = [...response]);
          
          console.log('数据已存储到 typhoonTrackPoints:', this.typhoonTrackPoints);
          console.log('typhoonTrackPoints 长度:', this.typhoonTrackPoints.length);
        } else {
          console.warn("API返回的数据为空或不是数组格式");
          this.typhoonTrackPoints = [];
        }
        
        // 显示台风轨迹
        this.displayTyphoonTrack();
      } catch (error) {
        console.error("加载台风轨迹失败:", error);
        alert("加载台风轨迹失败");
        this.typhoonTrackPoints = []; // 出错时清空数据
      }
    },

    // 显示台风轨迹
    displayTyphoonTrack() {
      // 不再调用 clearTyphoon，因为已经在 loadTyphoonTrack 中调用了

      // 重新初始化警戒线
      this.initJJ();

      this.myEntityCollection = new Cesium.CustomDataSource("typhoonTrackCollection")
      this.viewer.dataSources.add(this.myEntityCollection)

      // 更详细的调试和检查
      console.log('typhoonTrackPoints数据:', this.typhoonTrackPoints);
      console.log('typhoonTrackPoints类型:', typeof this.typhoonTrackPoints);
      console.log('typhoonTrackPoints长度:', Array.isArray(this.typhoonTrackPoints) ? this.typhoonTrackPoints.length : 'Not an array');

      // 修复：正确检查数据是否存在
      if (!Array.isArray(this.typhoonTrackPoints) || this.typhoonTrackPoints.length === 0) {
        alert("该台风暂无轨迹数据");
        return;
      }

     
      let lineArr = []

      this.typhoonTrackPoints.forEach((point, index) => {
        let color = Cesium.Color.RED
        
        // 使用正确的数据结构字段
        lineArr.push(Number(point.longitude))
        lineArr.push(Number(point.latitude))

        // 根据强度设置颜色（需要根据实际的level值调整）
        if (point.level === "TD") { // 热带低压
          color = Cesium.Color.YELLOW
        } else if (point.level === "TS") { // 热带风暴
          color = Cesium.Color.BLUE
        } else if (point.level === "STS") { // 强热带风暴
          color = Cesium.Color.GREEN
        } else if (point.level === "TY") { // 台风
          color = Cesium.Color.ORANGE
        } else if (point.level === "STY") { // 强台风
          color = Cesium.Color.MAGENTA
        } else if (point.level === "SuperTY") { // 超强台风
          color = Cesium.Color.RED
        }

        // 创建轨迹点实体
        var entity = new Cesium.Entity({
          position: Cesium.Cartesian3.fromDegrees(Number(point.longitude), Number(point.latitude)),
          point: {
            pixelSize: 8, // 增大点的大小便于拾取
            color: color,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 1
          },
          description: `
            <table class="cesium-infoBox-defaultTable">
              <tbody>
                <tr>
                  <th>时间</th>
                  <td>${point.timeStr}</td>
                </tr>
                <tr>
                  <th>强度</th>
                  <td>${this.getTyphoonLevelText(point.level)}</td>
                </tr>
                <tr>
                  <th>风速</th>
                  <td>${point.windSpeed} m/s</td>
                </tr>
                <tr>
                  <th>移向</th>
                  <td>${this.getDirectionText(point.direction)}</td>
                </tr>
                <tr>
                  <th>气压</th>
                  <td>${point.pressure} hPa</td>
                </tr>
                <tr>
                  <th>移动速度</th>
                  <td>${point.speed} km/h</td>
                </tr>
              </tbody>
            </table>
          `,
          // 为实体添加自定义属性，便于识别
          properties: {
            typhoonPoint: point
          },
          // 添加鼠标事件处理
          polyline: {
            show: false
          }
        })

        this.myEntityCollection.entities.add(entity)
      })

      // 绘制轨迹线
      const pathLine = this.viewer.entities.add({
        name: 'typhoonPath',
        polyline: {
          positions: Cesium.Cartesian3.fromDegreesArray(lineArr),
          width: 3,
          clampToGround: true,
          material: Cesium.Color.RED,
        }
      })
      this.typhoonEntities.push(pathLine);
       // 移动视角以包含所有路径点
      this.zoomToTyphoonPath();
      // 初始化台风动画状态
      this.currentTrackIndex = 0;
      this.isPlaying = false;
    },
 // 缩放到台风路径，使所有路径点可见
    zoomToTyphoonPath() {
      if (!this.typhoonTrackPoints || this.typhoonTrackPoints.length === 0) return;
      
      // 计算所有路径点的边界
      let minLon = Number.MAX_VALUE;
      let maxLon = -Number.MAX_VALUE;
      let minLat = Number.MAX_VALUE;
      let maxLat = -Number.MAX_VALUE;
      
      this.typhoonTrackPoints.forEach(point => {
        const lon = Number(point.longitude);
        const lat = Number(point.latitude);
        
        minLon = Math.min(minLon, lon);
        maxLon = Math.max(maxLon, lon);
        minLat = Math.min(minLat, lat);
        maxLat = Math.max(maxLat, lat);
      });
      
      // 添加一些边距
      const margin = 2.0;
      minLon -= margin;
      maxLon += margin;
      minLat -= margin;
      maxLat += margin;
      
      // 创建矩形范围
      const rectangle = Cesium.Rectangle.fromDegrees(minLon, minLat, maxLon, maxLat);
      
      // 计算相机视角
      const boundingSphere = Cesium.BoundingSphere.fromRectangle3D(rectangle, Cesium.Ellipsoid.WGS84);
      
      // 设置相机视角以包含所有点
      this.viewer.camera.flyToBoundingSphere(boundingSphere, {
        duration: 2,
        offset: new Cesium.HeadingPitchRange(
          0, 
          Cesium.Math.toRadians(-90), // 俯视角度
          boundingSphere.radius * 2.0 // 距离
        )
      });
    },
    // 切换台风动画播放/暂停状态
    toggleTyphoonAnimation() {
      if (!this.typhoonTrackPoints.length) {
        alert('请先加载台风数据');
        return;
      }

      if (this.isPlaying) {
        this.pauseTyphoonAnimation();
      } else {
        this.playTyphoonAnimation();
      }
    },

    // 播放台风动画
    playTyphoonAnimation() {
      if (!this.typhoonTrackPoints.length) return;
      
      this.isPlaying = true;
      
      // 如果是第一次播放且当前索引为0，则添加台风图标
      if (this.currentTrackIndex === 0 && !this.tbentity) {
        this.addTB().then(() => {
          this.startAnimationInterval();
        });
      } else {
        this.startAnimationInterval();
      }
    },

    // 开始动画定时器
    startAnimationInterval() {
      // 清除已存在的定时器
      if (this.typhoonAnimationInterval) {
        clearInterval(this.typhoonAnimationInterval);
      }
      
      // 启动动画定时器
      this.typhoonAnimationInterval = setInterval(() => {
        this.updateTyphoonPosition();
      }, 500); // 每500毫秒更新一次位置
    },

    // 暂停台风动画
    pauseTyphoonAnimation() {
      this.isPlaying = false;
      if (this.typhoonAnimationInterval) {
        clearInterval(this.typhoonAnimationInterval);
        this.typhoonAnimationInterval = null;
      }
    },

    // 更新台风位置
    updateTyphoonPosition() {
      if (!this.typhoonTrackPoints.length) return;
      
      // 获取当前轨迹点
      const currentPoint = this.typhoonTrackPoints[this.currentTrackIndex];
      
      // 更新台风图标位置
      if (this.tbentity) {
        this.tbentity.position = Cesium.Cartesian3.fromDegrees(
          Number(currentPoint.longitude),
          Number(currentPoint.latitude)
        );
      }
      
      // 更新台风影响范围
      this.currentPointObj = {
        lon: Number(currentPoint.longitude),
        lat: Number(currentPoint.latitude),
        circle7: {
          radius1: 350,
          radius2: 450,
          radius3: 400,
          radius4: 350,
        },
        circle10: {
          radius1: 250,
          radius2: 270,
          radius3: 250,
          radius4: 220,
        },
        circle12: {
          radius1: 170,
          radius2: 150,
          radius3: 150,
          radius4: 170,
        }
      };
      
      // 清除之前的影响范围并重新绘制
      this.removeTFLayer();
      this.addTyphoonCircle();
      
      // 更新索引
      this.currentTrackIndex++;
      
      // 如果播放完所有点，则重新开始
      if (this.currentTrackIndex >= this.typhoonTrackPoints.length) {
        this.currentTrackIndex = 0; // 重新开始
      }
    },

    // 预测
    initForeast(data) {
      // 这部分需要根据实际的预测数据结构进行调整
      if (!data.forecast) return;
      
      let forecast = data.forecast

      let colorArr = [
        Cesium.Color.fromCssColorString("#2D12FB"),
        Cesium.Color.fromCssColorString("#15E5E7"),
        Cesium.Color.fromCssColorString("#15E74A"),
        Cesium.Color.fromCssColorString("#E76F15"),
        Cesium.Color.fromCssColorString("#15D9E7"),
      ]

      forecast.forEach((ele, ii) => {
        let lineArr = []
        ele.forecastpoints.forEach((e) => {
          lineArr.push(Number(e.longitude))
          lineArr.push(Number(e.latitude))
          var entity1 = new Cesium.Entity({
            position: Cesium.Cartesian3.fromDegrees(Number(e.longitude), Number(e.latitude)),
            point: {
              pixelSize: 7,
              color: colorArr[ii]
            },
          })
          this.myEntityCollection.entities.add(entity1)
        })

        // 保存预测线实体引用
        const forecastLine = this.viewer.entities.add({
          polyline: {
            positions: Cesium.Cartesian3.fromDegreesArray(lineArr),
            width: 2,
            clampToGround: true,
            material: new Cesium.PolylineDashMaterialProperty({
              color: colorArr[ii]
            }),
          }
        })
        this.typhoonEntities.push(forecastLine);
      })
    },

    adds(data) {
      this.addTB()

      this.typhoonInterval = setInterval(() => {
        // 添加数据边界检查
        if (!data || data.length === 0) return;
        
        let kkk = this.iii * 2
        
        // 添加数据边界检查
        if (this.iii >= data.length) {
          this.iii = 0
        }
        
        const currentPoint = data[this.iii];
        
        this.currentPointObj = {
          lon: Number(currentPoint.longitude),
          lat: Number(currentPoint.latitude),
          circle7: {
            radius1: 350 - kkk,
            radius2: 450 - kkk,
            radius3: 400 - kkk,
            radius4: 350 - kkk,
          },
          circle10: {
            radius1: 250 - kkk,
            radius2: 270 - kkk,
            radius3: 250 - kkk,
            radius4: 220 - kkk,
          },
          circle12: {
            radius1: 170 - kkk,
            radius2: 150 - kkk,
            radius3: 150 - kkk,
            radius4: 170 - kkk,
          }
        }

        if (this.tbentity) {
          this.tbentity.position = Cesium.Cartesian3.fromDegrees(
            Number(currentPoint.longitude), 
            Number(currentPoint.latitude)
          )
        }

        if (this.iii > data.length - 1) {
          this.iii = 0
        } else {
          this.iii = this.iii + 1
        }

        this.removeTFLayer()
        this.addTyphoonCircle()
      }, 200)
    },

    addTB() {
      return new Promise((resolve) => {
        // 清除之前的 GIF 元素
        if (this.typhoonGifElement) {
          this.typhoonGifElement.remove();
          this.typhoonGifElement = null;
        }
        
        let div = document.createElement("div")
        let img = document.createElement("img")
        div.appendChild(img)
        img.src = '/images/tf.gif'
        this.typhoonGifElement = div; // 保存引用以便清理

        img.onload = () => {
          let rub = new SuperGif({
            gif: img
          })
          this.typhoonGif = rub; // 保存 GIF 对象引用

          rub.load(() => {
            // 确保在清除前实体还存在
            if (this.viewer && this.typhoonTrackPoints && this.typhoonTrackPoints.length > 0) {
              const firstPoint = this.typhoonTrackPoints[0];
              this.tbentity = this.viewer.entities.add({
                position: Cesium.Cartesian3.fromDegrees(
                  Number(firstPoint.longitude), 
                  Number(firstPoint.latitude)
                ),
                billboard: {
                  image: new Cesium.CallbackProperty(() => {
                    return rub.get_canvas().toDataURL("image/png")
                  }, false),
                  scale: 0.1,
                },
              })
            }
            resolve(this.tbentity)
          })
        }
        
        // 错误处理
        img.onerror = () => {
          console.error("加载台风 GIF 图片失败");
          // 如果加载失败，使用默认图标
          if (this.viewer && this.typhoonTrackPoints && this.typhoonTrackPoints.length > 0) {
            const firstPoint = this.typhoonTrackPoints[0];
            this.tbentity = this.viewer.entities.add({
              position: Cesium.Cartesian3.fromDegrees(
                Number(firstPoint.longitude), 
                Number(firstPoint.latitude)
              ),
              billboard: {
                image: '/images/tf.gif', // 直接使用图片路径
                scale: 0.1,
              },
            })
          }
          resolve(this.tbentity);
        }
      })
    },

    removeTFLayer() {
      let arr = this.fengquanLayers
      for (let i = 0; i < arr.length; i++) {
        this.viewer.entities.remove(arr[i])
      }
      this.fengquanLayers = []
    },

    addTyphoonCircle() {
      const circles = ["circle7", "circle10", "circle12"]
      circles.forEach(item => {
        let en = this.viewer.entities.add({
          id: `tf_polygon_${item}`,
          name: `tf_polygon_${item}`,
          polygon: {
            hierarchy:
              new Cesium.CallbackProperty(() => {
                let points = []
                if (this.currentPointObj && this.currentPointObj[item]) {
                  points = this.getTyphoonPolygonPoints(this.currentPointObj, item)
                } else {
                  points = []
                }
                return new Cesium.PolygonHierarchy(Cesium.Cartesian3.fromDegreesArray(points))
              }, false),
            material: Cesium.Color.ORANGE.withAlpha(0.05),
            extrudedHeight: 1000,
            outline: true,
            outlineColor: Cesium.Color.ORANGE,
            outlineWidth: 2,
          },
          polyline: {
            positions:
              new Cesium.CallbackProperty(() => {
                let points = []
                if (this.currentPointObj && this.currentPointObj[item]) {
                  points = this.getTyphoonPolygonPoints(this.currentPointObj, item)
                } else {
                  points = []
                }
                return Cesium.Cartesian3.fromDegreesArray(points)
              }, false),
            material: Cesium.Color.ORANGE,
            width: 2,
            height: 1000,
          }
        })
        this.fengquanLayers.push(en)
      })
    },

    // 获取台风圈面的坐标
    getTyphoonPolygonPoints(pointObj, cNum) {
      let points = []
      let center = [pointObj.lon * 1, pointObj.lat * 1]
      let radiusList = [
        pointObj[cNum]['radius1'],
        pointObj[cNum]['radius2'],
        pointObj[cNum]['radius3'],
        pointObj[cNum]['radius4'],
      ]

      let startAngleList = [0, 90, 180, 270]
      let fx, fy

      startAngleList.forEach((startAngle, index) => {
        let radius = radiusList[index] / 100
        let pointNum = 90
        let endAngle = startAngle + 90
        let sin, cos, x, y, angle

        for (let i = 0; i <= pointNum; i++) {
          angle = startAngle + ((endAngle - startAngle) * i) / pointNum
          sin = Math.sin((angle * Math.PI) / 180)
          cos = Math.cos((angle * Math.PI) / 180)
          x = center[0] + radius * sin
          y = center[1] + radius * cos
          points.push(x, y)

          if (startAngle == 0 && i == 0) {
            fx = x
            fy = y
          }
        }
      })

      points.push(fx, fy)
      return points
    },

    // 清除所有台风相关实体
    clearTyphoonEntities() {
      this.typhoonEntities.forEach(entity => {
        if (entity) {
          this.viewer.entities.remove(entity);
        }
      });
      this.typhoonEntities = [];
    },

    // 重写 clearTyphoon 方法
    clearTyphoon() {
      // 暂停动画
      this.pauseTyphoonAnimation();
      
      // 重置动画状态
      this.isPlaying = false;
      this.currentTrackIndex = 0;
      
      // 清除台风圈
      this.removeTFLayer()

      // 清除点线和预测数据
      if (this.myEntityCollection) {
        this.viewer.dataSources.remove(this.myEntityCollection)
        this.myEntityCollection = null
      }

      // 清除动画间隔
      if (this.typhoonInterval) {
        clearInterval(this.typhoonInterval)
        this.typhoonInterval = null
      }

      // 删除台风图标
      if (this.tbentity) {
        this.viewer.entities.remove(this.tbentity)
        this.tbentity = undefined
      }
      
      // 清理 GIF 对象
      if (this.typhoonGif) {
        this.typhoonGif = null;
      }
      
      // 清理 GIF DOM 元素
      if (this.typhoonGifElement) {
        this.typhoonGifElement.remove();
        this.typhoonGifElement = null;
      }

      // 清除警戒线和路径线
      this.clearTyphoonEntities();
      
      // 隐藏信息气泡
      this.hidePointInfoBubble();
      
      // 只有在有台风数据时才清除选中状态
      if (this.typhoonTrackPoints && this.typhoonTrackPoints.length > 0) {
        // 重置轨迹点数据
        this.typhoonTrackPoints = [];
      }
    },
    
    // 格式化时间显示
    formatTimeString(timeStr) {
      if (!timeStr || timeStr.length !== 12) return timeStr;
      const year = timeStr.substring(0, 4);
      const month = timeStr.substring(4, 6);
      const day = timeStr.substring(6, 8);
      const hour = timeStr.substring(8, 10);
      const minute = timeStr.substring(10, 12);
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    
    // 获取台风等级中文名称
    getTyphoonLevelText(level) {
      const levelMap = {
        'TD': '热带低压',
        'TS': '热带风暴',
        'STS': '强热带风暴',
        'TY': '台风',
        'STY': '强台风',
        'SuperTY': '超强台风'
      };
      return levelMap[level] || level;
    },
    
    // 获取台风移动方向的中文描述
    getDirectionText(direction) {
      const directionMap = {
        '0': '无',
        'N': '北',
        'NNE': '东北偏北',
        'NE': '东北',
        'ENE': '东北偏东',
        'E': '东',
        'ESE': '东南偏东',
        'SE': '东南',
        'SSE': '东南偏南',
        'S': '南',
        'SSW': '西南偏南',
        'SW': '西南',
        'WSW': '西南偏西',
        'W': '西',
        'WNW': '西北偏西',
        'NW': '西北',
        'NNW': '西北偏北'
      };
      return directionMap[direction] || direction;
    },
    
    // 点击台风轨迹点行时聚焦到该点
    focusOnTyphoonPoint(point) {
      if (!point) return;
      
      // 设置跟踪点
      this.trackedPoint = point;
      this.trackedPointCartesian = Cesium.Cartesian3.fromDegrees(
        Number(point.longitude),
        Number(point.latitude)
      );
      
      // 获取当前相机高度
      const cameraHeight = this.keepHeight;
      // 移动视角到该点，保持高度不变
      const position = Cesium.Cartesian3.fromDegrees(
        Number(point.longitude),
        Number(point.latitude),
        cameraHeight // 使用保持的高度
      );
      this.viewer.camera.flyTo({
        destination: position,
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-90),
          roll: 0
        },
        duration: 1
      });
      
      // 立即显示气泡
      this.showPointInfoBubble(point);
    },
    
    // 创建信息气泡容器
    createInfoBubble() {
      if (!document.getElementById('typhoon-info-bubble')) {
        const bubble = document.createElement('div');
        bubble.id = 'typhoon-info-bubble';
        bubble.className = 'info-bubble';
        document.body.appendChild(bubble);
      }
    },
    
    // 显示点信息气泡
    showPointInfoBubble(point) {
      if (!point) return;
      
      // 设置跟踪点
      this.trackedPoint = point;
      this.trackedPointCartesian = Cesium.Cartesian3.fromDegrees(
        Number(point.longitude),
        Number(point.latitude)
      );
      
      // 设置气泡内容
      this.bubbleContent = `
        <div class="bubble-info-item">
          <span class="info-label">时间:</span>
          <span class="info-value">${this.formatTimeString(point.timeStr)}</span>
        </div>
        <div class="bubble-info-item">
          <span class="info-label">风速:</span>
          <span class="info-value">${point.windSpeed} m/s</span>
        </div>
        <div class="bubble-info-item">
          <span class="info-label">等级:</span>
          <span class="info-value">${this.getTyphoonLevelText(point.level)}</span>
        </div>
        <div class="bubble-info-item">
          <span class="info-label">移向:</span>
          <span class="info-value">${this.getDirectionText(point.direction)}</span>
        </div>
        <div class="bubble-info-item">
          <span class="info-label">气压:</span>
          <span class="info-value">${point.pressure} hPa</span>
        </div>
        <div class="bubble-info-item">
          <span class="info-label">速度:</span>
          <span class="info-value">${point.speed} km/h</span>
        </div>
      `;
      
      // 初始位置计算
      if (this.viewer && this.viewer.scene) {
        const cartesian = Cesium.Cartesian3.fromDegrees(
          Number(point.longitude), 
          Number(point.latitude)
        );
        const canvasPos = this.viewer.scene.cartesianToCanvasCoordinates(cartesian);
        if (canvasPos) {
          // 修正：加上canvas的实际偏移
          const canvas = this.viewer.scene.canvas;
          const rect = canvas.getBoundingClientRect();
          this.bubblePosition = {
            x: canvasPos.x + rect.left,
            y: canvasPos.y + rect.top
          };
        }
      }
      
      // 显示气泡
      this.showInfoBubble = true;
    },
    
    // 隐藏信息气泡
    hidePointInfoBubble() {
      this.showInfoBubble = false;
      this.bubbleContent = '';
      this.trackedPoint = null;
      this.trackedPointCartesian = null;
    },
    
    // 处理鼠标移动事件
    handleMouseMovement(movement) {
      if (!this.viewer || !this.myEntityCollection) {
        return;
      }
      
      // 使用拾取功能检测鼠标是否悬停在实体上
      const pickedObject = this.viewer.scene.pick(movement);
      
      if (Cesium.defined(pickedObject) && 
          Cesium.defined(pickedObject.id) && 
          pickedObject.id instanceof Cesium.Entity &&
          pickedObject.id.properties && 
          pickedObject.id.properties.typhoonPoint) {
        // 悬停在台风轨迹点上
        const point = pickedObject.id.properties.typhoonPoint.getValue();
        this.showPointInfoBubble(point);
      } else {
        // 鼠标离开轨迹点
        this.hidePointInfoBubble();
      }
    },
    
    // 新增方法：更新气泡位置
    updateBubblePosition() {
      if (this.trackedPoint && this.showInfoBubble) {
        // 轨迹点经纬度转屏幕坐标
        if (this.viewer && this.viewer.scene) {
          // 使用当前 trackedPoint 的坐标
          const cartesian = Cesium.Cartesian3.fromDegrees(
            Number(this.trackedPoint.longitude), 
            Number(this.trackedPoint.latitude)
          );
          
          const canvasPos = this.viewer.scene.cartesianToCanvasCoordinates(cartesian);
          if (canvasPos) {
            // 修正：加上canvas的实际偏移
            const canvas = this.viewer.scene.canvas;
            const rect = canvas.getBoundingClientRect();
            this.bubblePosition = {
              x: canvasPos.x + rect.left,
              y: canvasPos.y + rect.top
            };
          }
        }
      }
    }
  },
  
}
</script>

<style scoped>
.typhoon-control-panel {
  position: absolute;
  top: 70px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 10px;
  color: white;
  min-width: 300px;
  font-size: 14px;
  font-family: 宋体;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-height: 80vh;
  overflow-y: auto;
}

.typhoon-main-control {
  top: 115px;
  right: 30px;
  left: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-header h4 {
  margin: 10px 0 5px;
  font-size: 14px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ff4d4f;
}

.typhoon-selector {
  margin-bottom: 15px;
}

.year-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.year-selector label {
  white-space: nowrap;
}

.year-selector select {
  flex: 1;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.typhoon-list {
  margin-bottom: 15px;
}

.typhoon-list h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.typhoon-items {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.typhoon-items li {
  padding: 8px 12px;
  border-bottom: 1px solid #444;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.typhoon-items li:hover {
  background-color: rgba(64, 158, 255, 0.3);
}

.typhoon-items li.active {
  background-color: rgba(64, 158, 255, 0.5);
}

.typhoon-basic-info {
  display: flex;
  flex-direction: column;
}

.typhoon-name {
  font-weight: bold;
}

.typhoon-en-name {
  font-size: 12px;
  color: #aaa;
}

.typhoon-code {
  color: #aaa;
  font-size: 12px;
}

.loading, .no-data {
  text-align: center;
  padding: 20px;
  color: #aaa;
}

/* 优化按钮组布局 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.button-row {
  display: flex;
  gap: 10px;
}

.button-row .cesium-button {
  flex: 1;
}

.clear-button {
  background-color: rgba(255, 69, 0, 0.8);
}

.clear-button:hover {
  background-color: rgba(255, 69, 0, 1);
}

.cesium-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100%;
}

.cesium-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.cesium-button:disabled {
  background-color: rgba(128, 128, 128, 0.5);
  cursor: not-allowed;
}

.main-control-panel {
  position: absolute;
  z-index: 1000;
}

.main-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 120px;
}

.main-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

/* 台风详细信息表格样式 */
.typhoon-detail {
  margin: 15px 0;
}

.typhoon-detail h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
}

.detail-table-container {
  max-height: 200px;
  overflow-y: auto;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.detail-table th,
.detail-table td {
  padding: 6px 8px;
  text-align: left;
  border-bottom: 1px solid #444;
}

.detail-table th {
  background-color: rgba(64, 158, 255, 0.3);
  position: sticky;
  top: 0;
}

.typhoon-track-row {
  cursor: pointer;
}

.typhoon-track-row:hover {
  background-color: rgba(64, 158, 255, 0.2);
}

.legend {
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  display: inline-block;
}

/* 信息气泡样式 */
.info-bubble {
  position: fixed;
  z-index: 9999;
  background: linear-gradient(135deg, rgba(30, 30, 40, 0.95), rgba(10, 10, 20, 0.95));
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1);
  color: #fff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-width: 250px;
  max-width: 300px;
  pointer-events: none;
  transform: translate(-50%, -100%);
  margin-top: -15px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(100, 150, 255, 0.3);
  transition: opacity 0.2s ease;
}

.bubble-content {
  padding: 12px;
}

.bubble-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
}

.bubble-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #4d9eff;
}

.close-btn {
  background: none;
  border: none;
  color: #aaa;
  font-size: 18px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ff4d4f;
}

.bubble-body {
  font-size: 13px;
}

.bubble-info-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.info-label {
  color: #aaa;
  font-weight: 500;
}

.info-value {
  color: #fff;
  font-weight: 500;
  text-align: right;
}

.bubble-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(30, 30, 40, 0.95);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .info-bubble {
    min-width: 200px;
    max-width: 250px;
  }
  
  .bubble-info-item {
    flex-direction: column;
    gap: 2px;
  }
  
  .info-value {
    text-align: left;
  }
}
</style>