<template>
  <div>
    <!-- 主控制面板 -->
    <div class="button-group">
      <!-- 电子围栏下拉菜单触发按钮 -->
      <button @click="handleToggleFenceMenu" class="cesium-button main-button">
        电子围栏 ▼
      </button>
    </div>
    
    <!-- 电子围栏下拉菜单 -->
    <div class="control-panel fence-panel" v-show="showFenceMenu">
      <div class="panel-header">
        <h3>电子围栏设置</h3>
        <button @click="handleToggleFenceMenu" class="close-button">×</button>
      </div>
      <div class="control-group">
        <button @click="handleToggleFence" class="cesium-button menu-button">
          {{ showFence ? '关闭围栏' : '开启围栏' }}
        </button>
      </div>
      <div class="control-group">
        <button @click="handleToggleWindBuffer" class="cesium-button menu-button">
          {{ showWindBuffer ? '隐藏风电缓冲区' : '显示风电缓冲区' }}
        </button>
      </div>
      
      <!-- 船舶位置查询设置 -->
      <div class="control-group">
        <h4>船舶位置查询</h4>
        <div class="ship-list">
          <div v-for="(mmsi, index) in shipMMSIs" :key="index" class="mmsi-item">
            <span>{{ mmsi }}</span>
            <button @click="removeShipMMSI(index)" class="remove-button">×</button>
          </div>
        </div>
        <input 
          v-model="newMMSI" 
          placeholder="输入MMSI编号" 
          class="mmsi-input"
          @keyup.enter="addShipMMSI"
        />
        <button @click="addShipMMSI" class="cesium-button menu-button">
          添加船舶
        </button>
        <button @click="fetchShipPositions" class="cesium-button menu-button" :disabled="shipMMSIs.length === 0">
          查询位置 ({{ shipMMSIs.length }}/10)
        </button>
        <div v-if="shipPositions.length > 0" class="ship-positions">
          <h4>船舶位置信息</h4>
          <div v-for="ship in shipPositions" :key="ship.mmsi" class="ship-info detailed">
            <div class="ship-header">
              <div class="ship-name">{{ ship.ship_name || '未知船舶' }} ({{ ship.mmsi }})</div>
              <div class="ship-flag" :class="getShipTypeClass(ship.ship_type)">
                {{ getShipTypeText(ship.ship_type) }}
              </div>
            </div>
            
            <div class="ship-details-grid">
              <div class="detail-item">
                <span class="detail-label">中文名:</span>
                <span class="detail-value">{{ ship.ship_cnname || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">IMO:</span>
                <span class="detail-value">{{ ship.imo || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">呼号:</span>
                <span class="detail-value">{{ ship.call_sign || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">位置:</span>
                <span class="detail-value">{{ ship.lat.toFixed(4) }}, {{ ship.lng.toFixed(4) }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">状态:</span>
                <span class="detail-value">{{ getNaviStatusText(ship.navistat) }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">航速:</span>
                <span class="detail-value">{{ ship.sog !== -1 ? ship.sog + ' 节' : 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">航向:</span>
                <span class="detail-value">{{ ship.cog !== -1 ? ship.cog + '度' : 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">航首向:</span>
                <span class="detail-value">{{ ship.hdg !== 511 ? ship.hdg + '度' : 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">尺寸:</span>
                <span class="detail-value">{{ ship.length ? ship.length + '米 × ' + ship.width + '米' : 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">吃水:</span>
                <span class="detail-value">{{ ship.draught ? ship.draught + '米' : 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">目的地:</span>
                <span class="detail-value">{{ ship.dest || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">预计到达:</span>
                <span class="detail-value">{{ ship.eta || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">最后更新:</span>
                <span class="detail-value">{{ ship.last_time || 'N/A' }}</span>
              </div>
              
              <div class="detail-item">
                <span class="detail-label">数据源:</span>
                <span class="detail-value">{{ ship.data_source === 0 ? 'AIS基站' : '卫星' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 围栏显示区域 -->
    <div v-show="visible">
      <!-- 围栏内容由现有逻辑处理 -->
    </div>
    
    <!-- 警示通知 -->
    <div v-if="alertMessage" class="alert-notification">
      <div class="alert-content">
        <span class="alert-icon">⚠️</span>
        <span>{{ alertMessage }}</span>
        <button @click="clearAlert" class="close-alert">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, watch, ref } from "vue";
import * as Cesium from "cesium";

// 接收 viewer、visible 和 fenceData 作为 prop
const props = defineProps({
  viewer: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  },
  fenceData: {
    type: Array,
    default: () => []
  },
  showFenceMenu: {
    type: Boolean,
    default: false
  },
  showFence: {
    type: Boolean,
    default: true // 修改默认值为true，使围栏默认显示
  },
  showWindBuffer: {
    type: Boolean,
    default: false // 保持默认不显示风电缓冲区
  }
});

// 定义 emits 用于与父组件通信
const emit = defineEmits([
  'update:windBufferDataSource', 
  'update:windBufferFenceData',
  'toggle-fence-menu',
  'toggle-fence',
  'toggle-wind-buffer'
]);

let wallEntities = [];
let datasource = null;
let isWallLoaded = ref(false);
let materialTypeRegistered = false;
let windBufferDataSource = null; // 风电缓冲区数据源

// 船舶相关
const shipMMSIs = ref([]);
const newMMSI = ref('');
const shipPositions = ref([]);

// 警示相关
const alertMessage = ref('');
const alertTimer = ref(null);

// 事件处理函数
function handleToggleFenceMenu() {
  emit('toggle-fence-menu');
}

function handleToggleFence() {
  emit('toggle-fence');
}

function handleToggleWindBuffer() {
  emit('toggle-wind-buffer');
}

// 船舶管理
function addShipMMSI() {
  if (newMMSI.value && !shipMMSIs.value.includes(newMMSI.value)) {
    if (shipMMSIs.value.length >= 10) {
      triggerAlert('最多只能添加10条船舶');
      return;
    }
    shipMMSIs.value.push(newMMSI.value);
    newMMSI.value = '';
  }
}

function removeShipMMSI(index) {
  shipMMSIs.value.splice(index, 1);
}

// 查询船舶位置
async function fetchShipPositions() {
  if (shipMMSIs.value.length === 0) return;
  
  try {
    const apiKey = '971f9a0725e74d1ebd0d647fd14925bb';
    const mmsis = shipMMSIs.value.join(',');
    const url = `https://api.shipxy.com/apicall/v3/GetManyShip?key=${apiKey}&mmsis=${mmsis}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === 0 && data.data) {
      shipPositions.value = Array.isArray(data.data) ? data.data : [data.data];
    } else {
      triggerAlert('船舶位置查询失败: ' + (data.msg || '未知错误'));
    }
  } catch (error) {
    console.error('查询船舶位置失败:', error);
    triggerAlert('查询船舶位置时发生错误');
  }
}

// 获取航行状态文本 (根据您提供的API文档更新)
function getNaviStatusText(status) {
  const statusMap = {
    0: '在航（主机推动）',
    1: '锚泊',
    2: '失控',
    3: '操纵受限',
    4: '吃水受限',
    5: '靠泊',
    6: '搁浅',
    7: '捕捞作业',
    8: '靠帆船提供动力'
  };
  return statusMap[status] || '未知状态';
}

// 获取船舶类型文本 (根据您提供的API文档更新)
function getShipTypeText(type) {
  // 特殊类型处理
  if (type >= 20 && type <= 29) return '地效应船';
  if (type >= 40 && type <= 49) return '高速船';
  if (type >= 60 && type <= 69) return '客船';
  if (type >= 70 && type <= 79) return '货船';
  if (type >= 80 && type <= 89) return '油轮';
  if (type >= 90 && type <= 99) return '其他类型的船舶';
  
  // 具体类型映射
  const typeMap = {
    10: '未知船舶',
    20: '地效应船',
    30: '捕捞',
    31: '拖引',
    32: '拖引并且船长>200m或船宽>25m',
    33: '疏浚或水下作业',
    34: '潜水作业',
    35: '参与军事行动',
    36: '帆船航行',
    37: '娱乐船',
    40: '高速船',
    50: '引航船',
    51: '搜救船',
    52: '拖轮',
    53: '港口供应船',
    54: '载有防污染装置和设备的船舶',
    55: '执法艇',
    56: '备用-用于当地船舶的任务分配',
    57: '备用-用于当地船舶的任务分配',
    58: '医疗船',
    59: '符合18号决议的船舶',
    60: '客船',
    70: '货船',
    80: '油轮',
    90: '其他类型的船舶',
    100: '集装箱'
  };
  
  return typeMap[type] || '未知类型';
}

// 获取船舶类型CSS类
function getShipTypeClass(type) {
  if (type >= 60 && type <= 69) return 'passenger-ship'; // 客船
  if (type >= 70 && type <= 79) return 'cargo-ship';     // 货船
  if (type >= 80 && type <= 89) return 'tanker-ship';    // 油轮
  if (type >= 90 && type <= 99) return 'other-ship';     // 其他类型
  if (type >= 50 && type <= 59) return 'special-ship';   // 特殊船舶
  if (type >= 30 && type <= 39) return 'fishing-ship';   // 捕捞/拖引等
  if (type >= 20 && type <= 29) return 'other-ship';     // 地效应船
  if (type >= 40 && type <= 49) return 'other-ship';     // 高速船
  if (type === 100) return 'other-ship';                 // 集装箱
  return 'other-ship';
}

// 检查船只是否进入围栏
function checkShipsInFence() {
  // 移除了船只监控功能，此函数可以保留为空或删除
}

// 检查单个船只是否进入围栏
async function checkShipInFence(mmsi) {
  // 移除了船只监控功能，此函数可以保留为空或删除
}

// 判断点是否在围栏内
function isPointInFence(point) {
  if (!props.fenceData || props.fenceData.length === 0) {
    return false;
  }
  
  // 使用射线法判断点是否在多边形内
  for (const fence of props.fenceData) {
    if (isPointInPolygon(point, fence)) {
      return true;
    }
  }
  
  return false;
}

// 判断点是否在多边形内（射线法）
function isPointInPolygon(point, polygon) {
  const [x, y] = point;
  let inside = false;
  
  // 遍历多边形的每条边
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];
    
    // 检查点是否在边的上方和下方之间，并且在边的左侧
    const intersect = ((yi > y) !== (yj > y)) && 
      (x < (xj - xi) * (y - yi) / (yj - yi) + xi);
    
    if (intersect) {
      inside = !inside;
    }
  }
  
  return inside;
}

// 触发警告
function triggerAlert(message) {
  alertMessage.value = message;
  
  // 清除之前的定时器
  if (alertTimer.value) {
    clearTimeout(alertTimer.value);
  }
  
  // 5秒后自动清除警告信息
  alertTimer.value = setTimeout(() => {
    alertMessage.value = '';
  }, 5000);
}

// 清除警告
function clearAlert() {
  alertMessage.value = '';
  if (alertTimer.value) {
    clearTimeout(alertTimer.value);
  }
}

/*
    动态墙材质
    color 颜色
    duration 持续时间 毫秒
    trailImage 贴图地址
*/
function DynamicWallMaterialProperty(options) {
  this._definitionChanged = new Cesium.Event();
  this._color = undefined;
  this._colorSubscription = undefined;
  this.color = options.color || Cesium.Color.BLUE;
  this.duration = options.duration || 1000;
  this.trailImage = options.trailImage;
  this._time = new Date().getTime();
}

/**
 * 带方向的墙体
 * @param {*} options.get:true/false
 * @param {*} options.count:数量
 * @param {*} options.freely:vertical/standard
 * * @param {*} options.direction:+/-
 */
function _getDirectionWallShader(options) {
  if (options && options.get) {
    var materail =
      "czm_material czm_getMaterial(czm_materialInput materialInput)\n\
      {\n\
          czm_material material = czm_getDefaultMaterial(materialInput);\n\
          vec2 st = materialInput.st;";
    if (options.freely == "vertical") {
      //（由下到上）
      materail +=
        "vec4 colorImage = texture(image, vec2(fract(st.s), fract(float(" +
        options.count +
        ")*st.t" +
        options.direction +
        " time)));\n";
    } else {
      //（逆时针）
      materail +=
        "vec4 colorImage = texture(image, vec2(fract(float(" +
        options.count +
        ")*st.s " +
        options.direction +
        " time), fract(st.t)));\n";
    }
    //泛光
    materail +=
      "vec4 fragColor;\n\
          fragColor.rgb = (colorImage.rgb+color.rgb) / 1.0;\n\
          fragColor = czm_gammaCorrect(fragColor);\n\
          material.diffuse = colorImage.rgb;\n\
          material.alpha = colorImage.a;\n\
          material.emission = fragColor.rgb;\n\
          return material;\n\
      }";
    return materail;
  }
}

Object.defineProperties(DynamicWallMaterialProperty.prototype, {
  isConstant: {
    get: function () {
      return false;
    },
  },
  definitionChanged: {
    get: function () {
      return this._definitionChanged;
    },
  },
  color: Cesium.createPropertyDescriptor("color"),
});

var MaterialType = "wallType" + parseInt(Math.random() * 1000);
DynamicWallMaterialProperty.prototype.getType = function (time) {
  return MaterialType;
};

DynamicWallMaterialProperty.prototype.getValue = function (time, result) {
  if (!Cesium.defined(result)) {
    result = {};
  }
  result.color = Cesium.Property.getValueOrClonedDefault(
    this._color,
    time,
    Cesium.Color.WHITE,
    result.color
  );
  result.image = this.trailImage;
  if (this.duration) {
    result.time =
      ((new Date().getTime() - this._time) % this.duration) / this.duration;
  }
  if (props.viewer) {
    props.viewer.scene.requestRender();
  }
  return result;
};

DynamicWallMaterialProperty.prototype.equals = function (other) {
  return (
    this === other ||
    (other instanceof DynamicWallMaterialProperty &&
      Cesium.Property.equals(this._color, other._color))
  );
};

function map_common_addDatasouce(datasouceName) {
  if (!props.viewer) return null;
  
  let datasouce = props.viewer.dataSources._dataSources.find((t) => {
    return t && t.name == datasouceName;
  });
  if (!datasouce) {
    datasouce = new Cesium.CustomDataSource(datasouceName);
    props.viewer.dataSources.add(datasouce);
  }
  return datasouce;
}

// 飞入围栏视角
function flyToFence() {
  if (!props.viewer || !props.fenceData || props.fenceData.length === 0) return;
  
  // 计算所有围栏的边界框
  let minLon = Infinity, maxLon = -Infinity;
  let minLat = Infinity, maxLat = -Infinity;
  
  props.fenceData.forEach(ring => {
    ring.forEach(point => {
      if (point[0] < minLon) minLon = point[0];
      if (point[0] > maxLon) maxLon = point[0];
      if (point[1] < minLat) minLat = point[1];
      if (point[1] > maxLat) maxLat = point[1];
    });
  });
  
  // 计算中心点
  const centerX = (minLon + maxLon) / 2;
  const centerY = (minLat + maxLat) / 2;
  
  // 计算适当的视图高度
  const lonDiff = maxLon - minLon;
  const latDiff = maxLat - minLat;
  const maxDiff = Math.max(lonDiff, latDiff);
  const distance = maxDiff * 150000; // 根据经度差估算合适的距离
  
  // 已禁用自动视角移动，保留边界计算逻辑
  // 如需恢复请取消注释
}

//加载范围
function loadWall() {
  if (!props.viewer || isWallLoaded.value || !props.fenceData || props.fenceData.length === 0) return;
  
  datasource = map_common_addDatasouce("wall");

  if (datasource) {
    // 为每个围栏环创建一个墙体实体
    props.fenceData.forEach((ring, index) => {
      if (ring.length === 0) return;
      
      let coor = Array.prototype.concat.apply([], ring);
      
      const wallEntity = datasource.entities.add({
        wall: {
          positions: Cesium.Cartesian3.fromDegreesArray(coor),
          maximumHeights: new Array(ring.length).fill(1000),
          minimumHeights: new Array(ring.length).fill(0),
          // 动态
          material: new DynamicWallMaterialProperty({
            trailImage: "/images/wall.png",
            color: Cesium.Color.CYAN,
            duration: 1500,
          }),
        },
      });
      
      wallEntities.push(wallEntity);
    });
    
    isWallLoaded.value = true;
    
    // 加载完成后不再自动飞入视角
    // flyToFence();
  }
}

// 移除墙体
function removeWall() {
  if (datasource && wallEntities.length > 0) {
    wallEntities.forEach(entity => {
      datasource.entities.remove(entity);
    });
    wallEntities = [];
    isWallLoaded.value = false;
  }
}

// 加载风电500m缓冲区GeoJSON数据
async function loadWindPowerBufferZone() {
  if (!props.viewer) return;
  
  try {
    // 创建风电缓冲区数据源
    const windPowerBufferDataSource = new Cesium.GeoJsonDataSource('windPowerBuffer');
    
    // 加载风电缓冲区数据
    await windPowerBufferDataSource.load('/Windpower/Windpower500m.json');
    
    // 提取缓冲区边界坐标用于围栏
    const allRings = [];
    
    windPowerBufferDataSource.entities.values.forEach(entity => {
      if (entity.polygon) {
        // 获取多边形边界坐标
        const hierarchy = entity.polygon.hierarchy.getValue();
        
        // 处理主环
        const positions = hierarchy.positions;
        
        // 转换为经纬度坐标
        const coords = positions.map(position => {
          const cartographic = Cesium.Cartographic.fromCartesian(position);
          return [
            Cesium.Math.toDegrees(cartographic.longitude),
            Cesium.Math.toDegrees(cartographic.latitude)
          ];
        });
        
        // 添加主环
        allRings.push(coords);
        
        // 处理孔洞（如果有）
        if (hierarchy.holes && hierarchy.holes.length > 0) {
          hierarchy.holes.forEach(hole => {
            const holePositions = hole.positions;
            const holeCoords = holePositions.map(position => {
              const cartographic = Cesium.Cartographic.fromCartesian(position);
              return [
                Cesium.Math.toDegrees(cartographic.longitude),
                Cesium.Math.toDegrees(cartographic.latitude)
              ];
            });
            // 添加孔洞环（注意：孔洞需要逆序绘制才能正确显示）
            allRings.push(holeCoords);
          });
        }
      }
      
      // 设置缓冲区样式（可选）
      if (entity.polygon) {
        // 设置半透明红色填充
        entity.polygon.material = Cesium.Color.RED.withAlpha(0.3);
        // 设置边框
        entity.polygon.outline = true;
        entity.polygon.outlineColor = Cesium.Color.RED;
        entity.polygon.outlineWidth = 1;
      }
    });
    
    // 保存风电缓冲区数据源引用并通知父组件
    windBufferDataSource = windPowerBufferDataSource;
    emit('update:windBufferDataSource', windPowerBufferDataSource);
    
    // 存储所有围栏数据并通知父组件
    emit('update:windBufferFenceData', allRings);
    
    // 添加到viewer
    props.viewer.dataSources.add(windPowerBufferDataSource);
    
    // 默认隐藏风电缓冲区
    windPowerBufferDataSource.show = false;
    
    console.log("风电500m缓冲区GeoJSON数据加载成功");
    return windPowerBufferDataSource;
  } catch (error) {
    console.error("风电500m缓冲区GeoJSON数据加载失败:", error);
  }
}

// 切换风电缓冲区显示
function toggleWindBuffer(showWindBuffer) {
  if (showWindBuffer && !windBufferDataSource) {
    // 首次显示时加载数据
    loadWindPowerBufferZone();
  } else if (windBufferDataSource) {
    // 切换显示状态
    windBufferDataSource.show = showWindBuffer;
  }
}

// 加载风电GeoJSON数据
async function loadWindPowerData() {
  if (!props.viewer) return;
  
  try {
    // 创建风电数据源
    const windPowerDataSource = new Cesium.GeoJsonDataSource('windPower');
    
    // 加载风电数据
    await windPowerDataSource.load('/Windpower/Windpower.json');
    
    // 设置风电区域为透明
    windPowerDataSource.entities.values.forEach(entity => {
      if (entity.polygon) {
        // 设置完全透明
        entity.polygon.material = Cesium.Color.TRANSPARENT;
        //设置边框线颜色为蓝色
        entity.polygon.outline = true;
        entity.polygon.outlineColor = Cesium.Color.BLUE;
      }
    });

    // 添加到viewer
    props.viewer.dataSources.add(windPowerDataSource);
    
    console.log("风电GeoJSON数据加载成功");
  } catch (error) {
    console.error("风电GeoJSON数据加载失败:", error);
  }
}

// 监听 viewer、visible 和 fenceData 的变化
watch([() => props.viewer, () => props.visible, () => props.fenceData], 
  ([newViewer, newVisible, newFenceData]) => {
    if (newViewer) {
      // 注册材质（只注册一次）
      if (!materialTypeRegistered) {
        Cesium.Material._materialCache.addMaterial(MaterialType, {
          fabric: {
            type: MaterialType,
            uniforms: {
              color: new Cesium.Color(1.0, 0.0, 0.0, 0.1),
              image: Cesium.Material.DefaultImageId,
              time: -20,
            },
            source: _getDirectionWallShader({
              get: true,
              count: 3.0,
              freely: "vertical",
              direction: "-",
            }),
          },
          translucent: function (material) {
            return true;
          },
        });
        materialTypeRegistered = true;
      }
      
      // 根据 visible 状态决定加载还是移除墙体
      if (newVisible && newFenceData && newFenceData.length > 0) {
        loadWall();
      } else {
        removeWall();
      }
      
      // 加载风电数据
      loadWindPowerData();
      
      // 默认加载风电缓冲区数据（但不显示）
      if (!windBufferDataSource) {
        loadWindPowerBufferZone();
      }
    }
  }, 
  { immediate: true }
);

// 组件卸载时清理
onUnmounted(() => {
  removeWall();
  if (alertTimer.value) {
    clearTimeout(alertTimer.value);
  }
});

// 暴露方法给父组件使用
defineExpose({
  toggleWindBuffer
});
</script>

<style scoped>
.control-panel {
  position: absolute;
  top: 70px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 10px;
  color: white;
  min-width: 180px;
  font-size: 14px;
  font-family: 宋体;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.fence-panel {
  top: 70px;
  right: 130px;
  left: auto;
  max-height: 80vh;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #444;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #ff4d4f;
}

.control-group {
  margin-bottom: 15px;
}

.control-group h4 {
  margin: 10px 0 5px 0;
  color: #409eff;
  font-size: 14px;
}

.cesium-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100%;
  margin-top: 5px;
}

.cesium-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.cesium-button:disabled {
  background-color: rgba(128, 128, 128, 0.8);
  cursor: not-allowed;
}

.main-button {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
  border: 1px solid #409eff;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 120px;
}

.main-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.mmsi-input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #409eff;
  background-color: rgba(255, 255, 255, 0.9);
}

.mmsi-list, .ship-list {
  max-height: 100px;
  overflow-y: auto;
  margin-bottom: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 5px;
}

.mmsi-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  border-bottom: 1px solid #444;
}

.mmsi-item:last-child {
  border-bottom: none;
}

.remove-button {
  background: none;
  border: none;
  color: #ff4d4f;
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ship-positions {
  margin-top: 15px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  max-height: 400px;
  overflow-y: auto;
}

.ship-info.detailed {
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  margin-bottom: 15px;
}

.ship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #444;
}

.ship-name {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.ship-flag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 10px;
  background: #666;
}

.ship-flag.passenger-ship {
  background: #409eff;
}

.ship-flag.cargo-ship {
  background: #4caf50;
}

.ship-flag.tanker-ship {
  background: #ff9800;
}

.ship-flag.fishing-ship {
  background: #9c27b0;
}

.ship-flag.special-ship {
  background: #ff5722;
}

.ship-flag.other-ship {
  background: #9e9e9e;
}

.ship-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.detail-item {
  display: flex;
  font-size: 13px;
}

.detail-label {
  font-weight: bold;
  color: #bbb;
  min-width: 70px;
  margin-right: 5px;
}

.detail-value {
  color: #fff;
  flex: 1;
}

.alert-notification {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 2000;
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
  max-width: 300px;
}

.alert-content {
  display: flex;
  align-items: center;
}

.alert-icon {
  font-size: 20px;
  margin-right: 10px;
}

.close-alert {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  margin-left: 15px;
  padding: 0;
  width: 24px;
  height: 24px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .ship-details-grid {
    grid-template-columns: 1fr;
  }
}
</style>